<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>TeamCheck | Streamlined Team Management</title>
    <meta name="title" content="TeamCheck | Streamlined Team Management" />
    <meta
      name="description"
      content="Effortlessly manage your team with TeamCheck's intuitive dashboard and Slack integration. Track attendance, tasks, performance, and leave requests in one place."
    />
    <meta
      name="keywords"
      content="team management, attendance tracking, task management, employee performance, slack integration, leave management"
    />
    <meta name="author" content="TeamCheck" />
    <meta name="application-name" content="TeamCheck" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://teamcheck.app/" />
    <meta
      property="og:title"
      content="TeamCheck | Streamlined Team Management"
    />
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <meta
      property="og:description"
      content="The lightweight team management platform that integrates with Slack. Monitor attendance, tasks, and performance without complexity."
    />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://teamcheck.app/" />
    <meta
      property="twitter:title"
      content="TeamCheck | Streamlined Team Management"
    />
    <meta
      property="twitter:description"
      content="Simplify team oversight with our intuitive dashboard and Slack integration. Perfect for small to medium teams."
    />
    <meta property="twitter:image" content="/twitter-image.png" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#0a0a0a" />
    <meta name="msapplication-TileColor" content="#0a0a0a" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
