{"name": "teamcheck-backend", "version": "1.0.0", "description": "Backend API for TeamCheck application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "seed": "sequelize-cli db:seed:all", "seed:undo": "sequelize-cli db:seed:undo:all"}, "keywords": ["teamcheck", "express", "mysql", "api"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-status": "^1.7.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "sequelize": "^6.35.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3"}}