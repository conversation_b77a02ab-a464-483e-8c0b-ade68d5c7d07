import React, { Suspense, useRef, useMemo } from "react";
import { <PERSON> } from "react-router-dom";
import { Canvas, useFrame } from "@react-three/fiber";
import { Float, Sphere, Torus } from "@react-three/drei";

// Simple Starry Night Scene
const StarryNightScene = () => {
  const starsRef = useRef();

  useFrame((state) => {
    const time = state.clock.elapsedTime;

    // Gentle rotation of the star field
    if (starsRef.current) {
      starsRef.current.rotation.y = time * 0.01;
      starsRef.current.rotation.x = Math.sin(time * 0.005) * 0.05;
    }
  });

  // Generate stars
  const stars = useMemo(() => {
    const starArray = [];
    for (let i = 0; i < 300; i++) {
      starArray.push({
        position: [
          (Math.random() - 0.5) * 150,
          (Math.random() - 0.5) * 80,
          (Math.random() - 0.5) * 80,
        ],
        size: Math.random() * 0.2 + 0.05,
        opacity: Math.random() * 0.8 + 0.2,
        twinkleSpeed: Math.random() * 2 + 0.5,
      });
    }
    return starArray;
  }, []);

  return (
    <group ref={starsRef}>
      {/* Stars */}
      {stars.map((star, index) => (
        <Float
          key={`star-${index}`}
          speed={star.twinkleSpeed}
          rotationIntensity={0.1}
          floatIntensity={0.1}
        >
          <Sphere args={[star.size, 6, 6]} position={star.position}>
            <meshBasicMaterial
              color="#ffffff"
              transparent
              opacity={star.opacity}
            />
          </Sphere>
        </Float>
      ))}
    </group>
  );
};

const ThreeBackground = () => {
  return (
    <div className="fixed inset-0 -z-10">
      <Canvas
        camera={{ position: [0, 0, 25], fov: 75 }}
        style={{ background: "transparent" }}
      >
        <ambientLight intensity={0.2} />
        <Suspense fallback={null}>
          <StarryNightScene />
        </Suspense>
      </Canvas>
    </div>
  );
};

// Main landing page component
const LandingPage = () => {
  return (
    <div className="overflow-hidden relative min-h-screen text-white bg-black">
      {/* Dark gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>

      {/* Content overlay */}
      <div className="relative z-10">
        {/* Header */}
        <header className="container absolute top-0 right-0 left-0 z-20 px-6 py-4 mx-auto">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <img
                src="/logo5.png"
                alt="TeamSzly Logo"
                className="w-auto h-8"
              />
              <h1 className="ml-2 text-lg font-semibold text-white">
                TeamSzly
              </h1>
            </div>
            <div className="flex items-center space-x-8">
              <nav className="hidden space-x-6 md:flex">
                <a
                  href="#features"
                  className="text-sm text-gray-400 transition-colors duration-300 hover:text-white"
                >
                  Features
                </a>
                <a
                  href="#pricing"
                  className="text-sm text-gray-400 transition-colors duration-300 hover:text-white"
                >
                  Pricing
                </a>
              </nav>
              <Link
                to="/login"
                className="text-sm text-gray-400 transition-colors duration-300 hover:text-white"
              >
                Login
              </Link>
            </div>
          </div>
        </header>

        <main>
          {/* Hero Section */}
          <section className="flex relative justify-center items-center pt-20 min-h-screen">
            <ThreeBackground />

            <div className="container px-6 mx-auto text-center">
              <div className="mx-auto space-y-8 max-w-4xl">
                {/* Hero Text */}
                <div className="space-y-6">
                  <h1 className="text-4xl font-bold leading-tight mg:text-5xl lg:text-7xl">
                    Team Oversight,
                    <br />
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
                      Simplified.
                    </span>
                  </h1>

                  <p className="mx-auto max-w-2xl text-sm leading-relaxed text-gray-400 lg:text-lg">
                    Your team's attendance, tasks, and performance—all in one
                    clean dashboard. Fast and focused—without the bloat.
                  </p>
                </div>

                {/* CTA Section */}
                <div className="space-y-6">
                  <div className="flex flex-col gap-4 justify-center items-center mx-auto max-w-lg sm:flex-row">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      className="flex-1 px-6 py-2 placeholder-gray-400 text-white rounded-lg border backdrop-blur-sm transition-all duration-300 lg:py-4 bg-white/10 border-white/20 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40"
                    />
                    <button className="px-8 py-2 font-semibold text-black whitespace-nowrap bg-white rounded-lg transition-all duration-300 lg:py-4 hover:bg-gray-100">
                      Join Waitlist →
                    </button>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-center items-center">
                      <span className="mr-2 w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                      <span className="text-sm text-gray-400">
                        First 3 months 50% off • 14-day free trial
                      </span>
                    </div>
                    <p className="text-sm font-medium text-white">
                      ✨ 100+ professionals have joined the waitlist
                    </p>
                  </div>
                </div>

                {/* Hero Image */}
                <div className="relative mx-auto mt-12 max-w-4xl">
                  <div className="absolute -inset-8 rounded-3xl blur-3xl bg-white/5"></div>
                  <div className="relative p-6 glass-card">
                    <img
                      src="/hero.png"
                      alt="TeamSzly Dashboard - Complete Team Management Solution"
                      className="w-full h-auto rounded-xl shadow-2xl"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src =
                          "https://via.placeholder.com/1200x800/1a1a1a/ffffff?text=TeamSzly+Dashboard+Preview";
                      }}
                    />
                    {/* Floating elements */}
                    <div className="absolute -top-4 -right-4 w-6 h-6 bg-white rounded-full shadow-lg animate-pulse shadow-white/50"></div>
                    <div className="absolute -bottom-4 -left-4 w-4 h-4 bg-white rounded-full shadow-lg animate-pulse shadow-white/50"></div>
                    <div className="absolute -left-6 top-1/2 w-3 h-3 bg-blue-400 rounded-full shadow-lg animate-pulse shadow-blue-400/50"></div>
                    <div className="absolute -right-6 top-1/4 w-3 h-3 bg-purple-400 rounded-full shadow-lg animate-pulse shadow-purple-400/50"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Features Section */}
          <section
            id="features"
            className="container px-6 py-16 mx-auto lg:py-32"
          >
            <div className="mb-20 text-center">
              <h2 className="mb-6 text-3xl font-bold text-white md:text-4xl lg:text-5xl">
                What TeamSzly Does
              </h2>
              <p className="mx-auto max-w-3xl text-gray-400 text0base lg:text-xl">
                Everything you need to manage your team effectively, reduce
                overhead, and boost productivity.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 mx-auto max-w-7xl md:grid-cols-2 lg:grid-cols-3">
              {/* Feature Card 1 */}
              <div className="p-6 border backdrop-blur-md transition-all duration-300 glass-card group hover:scale-105 bg-black/40 border-white/10">
                <div className="flex justify-center items-center mb-4 w-12 h-12 rounded-xl transition-transform duration-300 bg-white/10 group-hover:scale-110">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-xl font-bold text-white">
                  360° Team Overview
                </h3>
                <p className="text-sm tracking-normal leading-[180%] text-gray-400 flex flex-col gap-3">
                  <span className="font-medium text-gray-300">
                    Know Who's Doing What — and When.
                  </span>
                  <span>
                    {" "}
                    Get instant visibility into attendance, task progress, and
                    productivity. Everything you need to lead, all in one place.
                  </span>
                </p>
              </div>

              {/* Feature Card 2 */}
              <div className="p-6 border backdrop-blur-md transition-all duration-300 glass-card group hover:scale-105 bg-black/40 border-white/10">
                <div className="flex justify-center items-center mb-4 w-12 h-12 rounded-xl transition-transform duration-300 bg-white/10 group-hover:scale-110">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-xl font-bold text-white">
                  Automated Payroll
                </h3>
                <p className="text-sm tracking-normal leading-[180%] text-gray-400 flex flex-col gap-3">
                  <span className="font-medium text-gray-300">
                    No More Spreadsheets. No More Guesswork.
                  </span>
                  <span>
                    Run payrolls many times faster with accurate working hours
                    data. Eliminate manual errors and save hours every week.
                  </span>
                </p>
              </div>

              {/* Feature Card 3 */}
              <div className="p-6 border backdrop-blur-md transition-all duration-300 glass-card group hover:scale-105 bg-black/40 border-white/10">
                <div className="flex justify-center items-center mb-4 w-12 h-12 rounded-xl transition-transform duration-300 bg-white/10 group-hover:scale-110">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-xl font-bold text-white">
                  Reduced Meeting Time
                </h3>
                <p className="text-sm tracking-normal leading-[180%] text-gray-400 flex flex-col gap-3">
                  <span className="font-medium text-gray-300">
                    Meetings That Actually Make Sense.
                  </span>
                  <span>
                    Stay updated on what's going on without endless meetings.
                    Smart insights keep everyone aligned.
                  </span>
                </p>
              </div>

              {/* Feature Card 4 */}
              <div className="p-6 border backdrop-blur-md transition-all duration-300 glass-card group hover:scale-105 bg-black/40 border-white/10">
                <div className="flex justify-center items-center mb-4 w-12 h-12 rounded-xl transition-transform duration-300 bg-white/10 group-hover:scale-110">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-xl font-bold text-white">
                  Performance Analytics
                </h3>
                <p className="text-sm tracking-normal leading-[180%] text-gray-400 flex flex-col gap-3">
                  <span className="font-medium text-gray-300">
                    Turn Team Data into Team Wins.
                  </span>
                  <span>
                    Track performance trends, measure output, and spot
                    bottlenecks—before they become problems.
                  </span>
                </p>
              </div>

              {/* Feature Card 5 */}
              <div className="p-6 border backdrop-blur-md transition-all duration-300 glass-card group hover:scale-105 bg-black/40 border-white/10">
                <div className="flex justify-center items-center mb-4 w-12 h-12 rounded-xl transition-transform duration-300 bg-white/10 group-hover:scale-110">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-xl font-bold text-white">
                  Project-Based Hours Tracking
                </h3>
                <p className="text-sm tracking-normal leading-[180%] text-gray-400 flex flex-col gap-3">
                  <span className="font-medium text-gray-300">
                    Track Hours. Generate Bills. Get Paid.
                  </span>

                  <span>
                    Track project-based working hours for hourly workers to
                    generate accurate project bills and invoices.
                  </span>
                </p>
              </div>

              {/* Feature Card 6 */}
              <div className="p-6 border backdrop-blur-md transition-all duration-300 glass-card group hover:scale-105 bg-black/40 border-white/10">
                <div className="flex justify-center items-center mb-4 w-12 h-12 rounded-xl transition-transform duration-300 bg-white/10 group-hover:scale-110">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-xl font-bold text-white">
                  Security & Compliance
                </h3>
                <p className="text-sm tracking-normal leading-[180%] text-gray-400 flex flex-col gap-3">
                  <span className="font-medium text-gray-300">
                    Enterprise-Grade Security. Startup-Ready Speed.
                  </span>
                  Your team's data is encrypted, compliant, and protected—so you
                  can scale with confidence.
                </p>
              </div>
            </div>
          </section>

          {/* Pricing Section */}
          <section
            id="pricing"
            className="container px-6 py-16 mx-auto lg:py-32"
          >
            <div className="mb-12 text-center lg:mb-20">
              <h2 className="mb-6 text-4xl font-bold text-white lg:text-5xl">
                Pricing
              </h2>
              <p className="mx-auto max-w-3xl text-xl text-gray-400">
                Choose the plan that fits your team size and needs.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 mx-auto max-w-7xl md:grid-cols-2 lg:grid-cols-4">
              {/* Free Trial */}
              <div className="relative p-8 glass-card">
                <div className="text-center">
                  <div className="mb-4">
                    <span className="px-3 py-1 text-sm font-medium text-black bg-yellow-400 rounded-full">
                      🧪 Free Trial
                    </span>
                  </div>
                  <h3 className="mb-2 text-2xl font-bold text-white">
                    14-day free trial
                  </h3>
                  <p className="mb-6 text-gray-400">Credit card required</p>
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-white">Free</span>
                  </div>
                  <p className="mb-4 text-sm text-gray-400">
                    Maximum: 5 users per company
                  </p>
                  <p className="mb-6 text-sm text-gray-400">Cancel anytime</p>
                  <ul className="mb-8 space-y-2 text-sm text-left">
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Employee check-in/check-out
                    </li>
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Leave management
                    </li>
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Employee portal
                    </li>
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Project calendar view (daily, weekly, monthly)
                    </li>
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Basic performance evaluation
                    </li>
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Project activities management
                    </li>
                  </ul>
                </div>
              </div>

              {/* Starter Plan */}
              <div className="relative p-8 glass-card">
                <div className="text-center">
                  <div className="mb-4">
                    <span className="px-3 py-1 text-sm font-medium text-black bg-yellow-500 rounded-full">
                      🟡 Starter Plan
                    </span>
                  </div>
                  <h3 className="mb-2 text-2xl font-bold text-white">
                    $29/month
                  </h3>
                  <p className="mb-6 text-gray-400">includes up to 20 users</p>
                  <div className="mb-6">
                    <span className="text-lg text-gray-400">
                      $1/user/month beyond 20 users
                    </span>
                  </div>
                  <p className="mb-6 text-sm text-gray-400">
                    Includes all features from Free Trial, plus:
                  </p>
                  <ul className="mb-8 space-y-2 text-sm text-left">
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Dedicated advanced report and analytics system
                    </li>
                  </ul>
                </div>
              </div>

              {/* Pro Plan */}
              <div className="relative p-8 border-2 glass-card border-blue-500/30">
                <div className="text-center">
                  <div className="mb-4">
                    <span className="px-3 py-1 text-sm font-medium text-white bg-blue-500 rounded-full">
                      🔵 Pro Plan
                    </span>
                  </div>
                  <h3 className="mb-2 text-2xl font-bold text-white">
                    $99/month
                  </h3>
                  <p className="mb-6 text-gray-400">includes up to 50 users</p>
                  <div className="mb-6">
                    <span className="text-lg text-gray-400">
                      $2/user/month beyond 50 users
                    </span>
                  </div>
                  <p className="mb-6 text-sm text-gray-400">
                    Includes everything in Starter, plus:
                  </p>
                  <ul className="mb-8 space-y-2 text-sm text-left">
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Custom check-in/check-out questions
                    </li>
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Custom leave types
                    </li>
                  </ul>
                </div>
              </div>

              {/* Enterprise Plan */}
              <div className="relative p-8 glass-card">
                <div className="text-center">
                  <div className="mb-4">
                    <span className="px-3 py-1 text-sm font-medium text-white bg-gray-600 rounded-full">
                      🏢 Enterprise Plan
                    </span>
                  </div>
                  <h3 className="mb-2 text-2xl font-bold text-white">
                    Custom pricing
                  </h3>
                  <p className="mb-6 text-gray-400">Unlimited users</p>
                  <div className="mb-6">
                    <span className="text-lg text-gray-400">
                      Includes everything in Pro, plus:
                    </span>
                  </div>
                  <ul className="mb-8 space-y-2 text-sm text-left">
                    <li className="flex items-start text-gray-300">
                      <span className="mr-2 mt-1.5 w-1.5 h-1.5 bg-white rounded-full flex-shrink-0"></span>
                      Custom feature development (by request)
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* Waitlist CTA Section */}
          <section
            id="waitlist"
            className="container px-6 py-16 mx-auto lg:py-32"
          >
            <div className="mx-auto max-w-4xl">
              <div className="relative">
                <div className="absolute -inset-8 rounded-3xl blur-3xl bg-white/5"></div>
                <div className="relative p-6 text-center glass-card lg:p-16">
                  <div className="space-y-8">
                    <h2 className="text-3xl font-bold leading-tight lg:text-6xl">
                      <span className="text-white">Ready to Transform</span>
                      <br />
                      <span className="text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-400">
                        Your Team?
                      </span>
                    </h2>

                    <p className="mx-auto max-w-2xl text-base leading-relaxed text-gray-300 lg:text-xl">
                      Join the waitlist and be among the first to experience the
                      future of team management. Get exclusive benefits when you
                      sign up early.
                    </p>

                    <div className="flex flex-col gap-4 mx-auto max-w-lg sm:flex-row">
                      <input
                        type="email"
                        placeholder="Enter your email"
                        className="flex-1 px-6 py-2 placeholder-gray-400 text-white rounded-lg border backdrop-blur-sm transition-all duration-300 lg:py-4 bg-white/10 border-white/20 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40"
                      />
                      <button className="px-8 py-2 font-semibold text-black whitespace-nowrap bg-white rounded-lg transition-all duration-300 lg:py-4 hover:bg-gray-100">
                        Join Waitlist
                      </button>
                    </div>

                    <div className="flex flex-wrap gap-2 justify-center text-sm text-gray-400 lg:gap-8">
                      <div className="flex items-center">
                        <svg
                          className="mr-2 w-5 h-5 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        First 3 months 50% off
                      </div>
                      <div className="flex items-center">
                        <svg
                          className="mr-2 w-5 h-5 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        14-day free trial
                      </div>
                      <div className="flex items-center">
                        <svg
                          className="mr-2 w-5 h-5 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        Priority support
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>

        {/* Footer */}
        <footer className="container px-6 py-16 mx-auto border-t border-white/10">
          <div className="flex flex-col justify-between items-center space-y-8 md:flex-row md:space-y-0">
            <div className="flex items-center">
              <img
                src="/logo5.png"
                alt="TeamSzly Logo"
                className="w-auto h-10"
              />
              <h2 className="ml-3 text-xl font-semibold text-white">
                TeamSzly
              </h2>
            </div>

            <div className="flex items-center space-x-8 text-sm text-gray-400">
              <a
                href="#features"
                className="transition-colors duration-300 hover:text-white"
              >
                Features
              </a>
              <a
                href="#waitlist"
                className="transition-colors duration-300 hover:text-white"
              >
                Waitlist
              </a>
            </div>

            <div className="text-sm text-gray-400">
              &copy; {new Date().getFullYear()} TeamSzly. All rights reserved.
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default LandingPage;
